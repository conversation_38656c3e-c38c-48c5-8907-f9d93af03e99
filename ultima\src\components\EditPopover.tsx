import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ReactNode, useEffect, useCallback, useRef } from "react";
import * as PopoverPrimitive from "@radix-ui/react-popover";

interface EditPopoverProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  value: string;
  onValueChange: (value: string) => void;
  onSubmit: () => void;
  children: ReactNode;
}

export function EditPopover({
  open,
  onOpenChange,
  value,
  onValueChange,
  onSubmit,
  children,
}: EditPopoverProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleOpenChange = useCallback((newOpen: boolean) => {
    onOpenChange(newOpen);
  }, [onOpenChange]);

  const handleKeyUp = (e: React.KeyboardEvent<HTMLInputElement>) => {
    e.stopPropagation();
    if (e.key === "Enter") {
      onSubmit();
    } else if (e.key === "Escape") {
      onOpenChange(false);
    }
  };

  useEffect(() => {
    if (open) {
      // Find the closest parent dialog element to this popover
      const findParentDialog = () => {
        // Wait for the popover content to be rendered in the portal
        const popoverContent = document.querySelector('[data-radix-popover-content]');
        if (!popoverContent) return null;

        // Find all dialog elements and determine which one is the parent modal
        const dialogElements = Array.from(document.querySelectorAll('[role="dialog"]')) as HTMLElement[];

        // Return the dialog that's currently open and visible
        return dialogElements.find(dialog => {
          const isOpen = dialog.getAttribute('data-state') === 'open' ||
                        !dialog.hasAttribute('data-state'); // fallback for dialogs without data-state
          const isVisible = dialog.offsetParent !== null; // check if element is visible
          return isOpen && isVisible;
        });
      };

      // Disable modal focus trap for the specific parent dialog
      const disableFocusTrap = () => {
        const dialogElement = findParentDialog();
        if (dialogElement) {
          dialogElement.setAttribute('data-modal-disabled', 'true');
          // Remove tabindex to disable focus trap
          dialogElement.removeAttribute('tabindex');
        }
        return dialogElement;
      };

      // Focus the input after modal focus trap is disabled
      const focusInput = () => {
        if (inputRef.current) {
          inputRef.current.focus();
          inputRef.current.select();
        }
      };

      // Use requestAnimationFrame for better timing
      requestAnimationFrame(() => {
        const dialogElement = disableFocusTrap();
        if (dialogElement) {
          // Small delay to ensure the popover is fully rendered
          setTimeout(focusInput, 50);
        } else {
          // If no dialog found, focus immediately
          focusInput();
        }
      });
    } else {
      // Re-enable modal focus trap when popover closes
      requestAnimationFrame(() => {
        // Find all dialogs that were disabled by this popover
        const disabledDialogs = Array.from(document.querySelectorAll('[role="dialog"][data-modal-disabled="true"]')) as HTMLElement[];

        disabledDialogs.forEach(dialogElement => {
          // Check if there are any other open popovers in this dialog
          const openPopoversInDialog = dialogElement.querySelectorAll('.edit-popover-input').length;

          if (openPopoversInDialog === 0) {
            // No more open popovers, re-enable focus trap
            dialogElement.removeAttribute('data-modal-disabled');
            dialogElement.setAttribute('tabindex', '-1');
          }
        });
      });
    }
  }, [open]);

  return (
    <Popover 
      open={open} 
      onOpenChange={handleOpenChange}
    >
      <PopoverTrigger asChild>
        <div className="flex items-center">
          {children}
        </div>
      </PopoverTrigger>
      <PopoverPrimitive.Portal>
        <PopoverPrimitive.Content 
          className="z-[60] w-48 rounded-md border bg-popover p-2 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 pointer-events-auto"
          sideOffset={4}
          align="center"
          onInteractOutside={(e) => {
            const target = e.target as HTMLElement;
            if (target.closest('[role="dialog"]') || target.closest('[data-trigger="true"]')) {
              e.preventDefault();
            }
          }}
          onOpenAutoFocus={(e) => {
            e.preventDefault();
          }}
        >
          <div className="flex flex-col gap-2">
            <Input
              ref={inputRef}
              autoFocus
              type="text"
              value={value}
              onChange={(e) => {
                e.stopPropagation();
                onValueChange(e.target.value);
              }}
              onKeyUp={handleKeyUp}
              className="text-sm edit-popover-input"
            />
            <Button 
              size="sm" 
              onClick={(e) => {
                e.stopPropagation();
                onSubmit();
              }} 
              className="w-full"
            >
              Save
            </Button>
          </div>
          <PopoverPrimitive.Arrow className="fill-border" />
        </PopoverPrimitive.Content>
      </PopoverPrimitive.Portal>
    </Popover>
  );
}
